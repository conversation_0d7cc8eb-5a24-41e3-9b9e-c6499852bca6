import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/discovery/repository/discovery_repository.dart';

part 'friends_event.dart';
part 'friends_state.dart';

class FriendsBloc extends Bloc<FriendsEvent, FriendsState> {
  final DiscoveryRepository _discoveryRepository;

  FriendsBloc(this._discoveryRepository) : super(FriendsInitial()) {
    on<LoadFriends>(_onLoadFriends);
  }

  Future<void> _onLoadFriends(
    LoadFriends event,
    Emitter<FriendsState> emit,
  ) async {
    emit(FriendsLoading());
    try {
      // 复用 DiscoveryRepository 来获取角色列表
      // 在实际应用中，这里可能会调用一个专门的 /api/friends 接口
      final agents = await _discoveryRepository.fetchAgents();
      emit(FriendsLoaded(agents));
    } catch (e) {
      emit(FriendsError(e.toString()));
    }
  }
}