// lib/src/features/friends/presentation/pages/friends_page.dart

import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';

// 星空背景绘制器
class _StarrySkyPainter extends CustomPainter {
  final List<_Star> stars;
  _StarrySkyPainter(this.stars);
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    for (final star in stars) {
      paint.color = Colors.white.withOpacity(star.opacity);
      canvas.drawCircle(star.position, star.size, paint);
    }
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _Star {
  final Offset position;
  final double size;
  final double opacity;
  _Star(this.position, this.size, this.opacity);
}

class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage> {
  late List<_Star> _stars;

  @override
  void initState() {
    super.initState();
    _stars = _generateStars(300);
  }

  List<_Star> _generateStars(int count) {
    final random = math.Random();
    return List.generate(count, (index) {
      return _Star(
        Offset(random.nextDouble() * 2000, random.nextDouble() * 2000),
        random.nextDouble() * 1.5 + 0.5,
        random.nextDouble() * 0.8 + 0.2,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        automaticallyImplyLeading: false,
        centerTitle: true, // 确保自定义标题能够居中
        // --- ▼▼▼ 核心修改区域开始 ▼▼▼ ---
        title: _buildGlowingTitle('羁', '绊'),
        // --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---
        actions: [
          // --- ▼▼▼ 核心修改区域开始 ▼▼▼ ---
          Container(
            margin: const EdgeInsets.only(right: 8.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.accentPurple.withOpacity(0.4),
                        AppColors.accentPurple.withOpacity(0.2),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.white.withOpacity(0.2)),
                  ),
                  child: TextButton.icon(
                    onPressed: () {},
                    icon: const Icon(Icons.add_circle_outline, color: AppColors.primaryText, size: 20),
                    label: const Text(
                      '心伴小窗',
                      style: TextStyle(color: AppColors.primaryText, fontSize: 14),
                    ),
                    style: TextButton.styleFrom(
                      // 1. 再次减小垂直内边距，让按钮更窄
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      // 2. 移除最小尺寸限制，让按钮可以根据内容自由收缩
                      minimumSize: Size.zero,
                      // 3. 确保点击区域不会增加额外的内边距，实现视觉上的紧凑
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                    ),
                  ),
                ),
              ),
            ),
          ),
          // --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---
        ],
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: CustomPaint(
              painter: _StarrySkyPainter(_stars),
            ),
          ),
          SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // --- ▼▼▼ 核心修改 5：新增“羁绊邀约”标题 ▼▼▼ ---
                _buildSectionHeader(icon: Icons.mail_outline, title: '羁绊邀约'),
                // --- ▲▲▲ 修改结束 ▲▲▲ ---
                _buildTopFunctionCards(),
                const SizedBox(height: 24),
                // --- ▼▼▼ 核心修改 6：新增“羁绊关系”标题 ▼▼▼ ---
                _buildSectionHeader(icon: Icons.favorite_border, title: '羁绊关系'),
                // --- ▲▲▲ 修改结束 ▲▲▲ ---
                _RelationshipSection(
                  title: '初见',
                  characterCount: _getInitialCharacters().length,
                  levelRequired: 1,
                  characters: _getInitialCharacters(),
                  initiallyExpanded: true,
                ),
                _RelationshipSection(
                  title: '恋人',
                  characterCount: _getLoverCharacters().length,
                  levelRequired: 10,
                  characters: _getLoverCharacters(),
                ),
                _RelationshipSection(
                  title: '朋友',
                  characterCount: _getFriendCharacters().length,
                  levelRequired: 7,
                  characters: _getFriendCharacters(),
                ),
                _RelationshipSection(
                  title: '熟人',
                  characterCount: _getFamilyCharacters().length,
                  levelRequired: 4,
                  characters: _getFamilyCharacters(),
                ),
                const SizedBox(height: 100),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
// --- ▼▼▼ 核心修改区域：为辉光标题添加图标 ▼▼▼ ---
Widget _buildGlowingTitle(String firstChar, String secondChar) {
  return Row(
    mainAxisSize: MainAxisSize.min, // 保证Row只占用所需宽度，从而能在AppBar中居中
    crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐图标和文字
    children: [
      // 新增的 Sparkle 图标
      Icon(
        Icons.auto_awesome, // 闪耀图标
        color: Colors.white.withOpacity(0.9), // 半透明白色，符合UI风格
        size: 22,
        shadows: [
          // 为图标也加上辉光效果
          Shadow(
            color: Colors.white.withOpacity(0.5),
            blurRadius: 10.0,
          ),
        ],
      ),
      const SizedBox(width: 8), // 图标和文字的间距

      // 原有的错落文字效果
      Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            firstChar,
            style: TextStyle(
              fontFamily: 'BaiWuChangKeKeTi', // <--- 在这里应用字体
              fontSize: 26, // 第一个字更大
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.white.withOpacity(0.6),
                  blurRadius: 15.0,
                ),
                 Shadow(
                  color: Colors.white.withOpacity(0.3),
                  blurRadius: 25.0,
                ),
              ],
            ),
          ),
          const SizedBox(width: 4), // 字符间距
          Text(
            secondChar,
            style: TextStyle(
              fontFamily: 'BaiWuChangKeKeTi', // <--- 在这里应用字体
              fontSize: 20, // 第二个字较小
              fontWeight: FontWeight.bold,
              color: Colors.white,
               shadows: [
                Shadow(
                  color: Colors.white.withOpacity(0.5),
                  blurRadius: 10.0,
                ),
              ],
            ),
          ),
        ],
      ),
    ],
  );
}
// --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---


  // --- ▼▼▼ 核心修改 5 & 6：创建可复用的分区标题组件 ▼▼▼ ---
  Widget _buildSectionHeader({required IconData icon, required String title}) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 12.0),
      child: Row(
        children: [
          // 核心修改 1: 增大图标尺寸并改为粉紫色
          Icon(icon, color: AppColors.accentPurple, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              // 核心修改 2: 增大字号、加粗并改为粉紫色
              color: AppColors.accentPurple,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  // --- ▲▲▲ 修改结束 ▲▲▲ ---


  Widget _buildTopFunctionCards() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 0),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 1,
              child: _buildFunctionCard(
                title: '拾光之约',
                subtitle: '末显影 那一夜',
                icon: Icons.photo_library,
                onTap: () {},
                isLarge: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  Expanded(
                    child: _buildFunctionCard(
                      title: '留影室',
                      subtitle: '留下美好瞬间',
                      icon: Icons.camera_alt,
                      onTap: () {},
                    ),
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: _buildFunctionCard(
                      title: '次元诊疗',
                      subtitle: 'TA确诊了肌肤饥渴症',
                      icon: Icons.healing,
                      onTap: () {},
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    bool isLarge = false,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.0),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.secondaryBg.withOpacity(0.5),
              borderRadius: BorderRadius.circular(16.0),
              border: Border.all(color: Colors.white.withOpacity(0.1)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(icon, color: Colors.white, size: isLarge ? 20 : 16),
                    const SizedBox(width: 8),
                    Text(title, style: TextStyle(color: AppColors.primaryText, fontSize: isLarge ? 16 : 14, fontWeight: FontWeight.bold)),
                  ],
                ),
                Text(subtitle, style: TextStyle(color: AppColors.secondaryText, fontSize: isLarge ? 12 : 10)),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildCharacterCard(Map<String, dynamic> character) {
    final bondProgress = (character['bondProgress'] as int).toDouble();
    final maxBond = (character['maxBond'] as int).toDouble();
    final bondPercentage = (bondProgress / maxBond).clamp(0.0, 1.0);

    return InkWell(
      onTap: () {
        if (character['isUnlocked']) {
          context.push('/chat/${character['chatId']}');
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 120,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.inputBackground,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(character['avatar'], fit: BoxFit.cover),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(character['name'], style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold)),
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.accentPurple.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text('Lv.${character['level']}', style: const TextStyle(color: AppColors.accentPurple, fontSize: 10, fontWeight: FontWeight.bold)),
                      )
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('羁绊值 ${bondProgress.toInt()}/${maxBond.toInt()}', style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                      const SizedBox(height: 4),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: bondPercentage,
                          backgroundColor: Colors.white.withOpacity(0.1),
                          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.accentPurple),
                          minHeight: 5,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Icon(Icons.lock_open, size: 12, color: AppColors.secondaryText),
                      const SizedBox(width: 4),
                      Text(character['unlockDescription'], style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _CircularUnlockProgress(progress: (character['unlockProgress'] as int)),
                Container(
                  height: 28,
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(14),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.accentPurple.withOpacity(0.3),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      )
                    ],
                  ),
                  child: ElevatedButton.icon(
                    onPressed: () {},
                    icon: const Icon(Icons.chat_bubble, size: 12, color: Colors.white),
                    label: const Text('邀请TA', style: TextStyle(fontSize: 10, color: Colors.white, fontWeight: FontWeight.bold)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _CircularUnlockProgress({required int progress}) {
    return SizedBox(
      width: 40,
      height: 40,
      child: Stack(
        fit: StackFit.expand,
        children: [
          CircularProgressIndicator(
            value: 1.0,
            strokeWidth: 3,
            color: AppColors.secondaryBg,
          ),
          Transform(
            alignment: Alignment.center,
            transform: Matrix4.rotationY(math.pi),
            child: CircularProgressIndicator(
              value: progress / 100.0,
              strokeWidth: 3,
              color: AppColors.accentPurple,
              strokeCap: StrokeCap.round,
            ),
          ),
          Center(
            child: Text(
              '$progress%',
              style: const TextStyle(
                color: AppColors.primaryText,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          )
        ],
      ),
    );
  }

  // (模拟数据保持不变)
  List<Map<String, dynamic>> _getInitialCharacters() {
    return [
      {
        'name': '傅止洲',
        'avatar': 'https://i.imgur.com/your_image_1.png',
        'level': 2,
        'bondProgress': 5,
        'maxBond': 30,
        'unlockProgress': 0,
        'isUnlocked': true,
        'chatId': 'chat_fuzhizhou',
        'unlockDescription': '渴望依赖的小甜心',
      },
      {
        'name': '年岁安',
        'avatar': 'https://i.imgur.com/your_image_2.png',
        'level': 1,
        'bondProgress': 1,
        'maxBond': 5,
        'unlockProgress': 0,
        'isUnlocked': true,
        'chatId': 'chat_niansuian',
        'unlockDescription': '还没有解锁TA的日记哦~',
      },
    ];
  }
  List<Map<String, dynamic>> _getLoverCharacters() => [];
  List<Map<String, dynamic>> _getFriendCharacters() => [];
  List<Map<String, dynamic>> _getFamilyCharacters() {
     return [
      {
        'name': '熟人',
        'avatar': 'https://i.imgur.com/family1.png',
        'level': 4,
        'bondProgress': 95,
        'maxBond': 100,
        'unlockProgress': 95,
        'isUnlocked': false,
        'chatId': 'chat_family1',
        'unlockDescription': '关系还需升温',
      },
    ];
  }
}

class _RelationshipSection extends StatefulWidget {
  final String title;
  final int characterCount;
  final int levelRequired;
  final List<Map<String, dynamic>> characters;
  final bool initiallyExpanded;

  const _RelationshipSection({
    required this.title,
    required this.characterCount,
    required this.levelRequired,
    required this.characters,
    this.initiallyExpanded = false,
  });

  @override
  State<_RelationshipSection> createState() => _RelationshipSectionState();
}

class _RelationshipSectionState extends State<_RelationshipSection> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = Tween(begin: 0.0, end: 0.25).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTap(bool isExpanding) {
    setState(() {
      _isExpanded = isExpanding;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // --- ▼▼▼ 核心修改 2 & 3：使用 Theme 小组件来精细控制间距，并移除分组背景 ▼▼▼ ---
    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent, // 移除 ExpansionTile 默认的分割线
        listTileTheme: const ListTileThemeData(
          horizontalTitleGap: 8.0, // 精确控制 leading 和 title 之间的距离
        ),
      ),
      child: ExpansionTile(
        initiallyExpanded: widget.initiallyExpanded,
        onExpansionChanged: _handleTap,
        // 移除所有默认边框
        shape: const Border(),
        collapsedShape: const Border(),
        // 背景设为透明
        backgroundColor: Colors.transparent,
        collapsedBackgroundColor: Colors.transparent,
        // 使用 leading 属性放置旋转图标
        leading: RotationTransition(
          turns: _animation,
          child: const Icon(Icons.play_arrow, size: 16, color: AppColors.secondaryText),
        ),
        trailing: const SizedBox.shrink(), // 移除默认的右侧箭头
        title: Row(
          children: [
            Text('${widget.title} (${widget.characterCount})', style: const TextStyle(color: AppColors.primaryText, fontSize: 16, fontWeight: FontWeight.bold)),
            const Spacer(),
            Text('羁绊值Lv.${widget.levelRequired}', style: const TextStyle(color: AppColors.secondaryText, fontSize: 12)),
          ],
        ),
        // --- ▼▼▼ 核心修改：将垂直内边距减半以收窄高度 ▼▼▼ ---
        tilePadding: const EdgeInsets.symmetric(vertical: 2.0), // 从 12.0 改为 6.0
        // --- ▲▲▲ 修改结束 ▲▲▲ ---
        childrenPadding: const EdgeInsets.only(top: 8, bottom: 8),
        children: [
          if (widget.characters.isEmpty)
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: Text('暂无角色', style: TextStyle(color: AppColors.secondaryText)),
            )
          else
            for (var char in widget.characters) ...[
              (context.findAncestorStateOfType<_FriendsPageState>())!._buildCharacterCard(char),
              if (char != widget.characters.last) const SizedBox(height: 12),
            ]
        ],
      ),
    );
    // --- ▲▲▲ 修改结束 ▲▲▲ ---
  }
}