name: xinglian
description: A Flutter clone of the Xinglian app.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  intl: ^0.19.0 # <--- 添加这行 (版本号可能需要根据你的项目调整)
  # 路由管理 - 方便进行复杂的页面跳转和参数传递
  go_router: ^14.1.0

  # 状态管理 - 后续动态化页面必备 (此处选择 Bloc，你也可以用Riverpod)
  flutter_bloc: ^8.1.5
  bloc: ^8.1.4

  # BLoC模式最佳实践 - 用于状态比较
  equatable: ^2.0.5

  # 瀑布流布局
  flutter_staggered_grid_view: ^0.7.0

  # 本地存储 - 用于持久化用户设置
  shared_preferences: ^2.2.3

  # 网络请求库
  dio: ^5.4.3+1

  # Provider状态管理 - 用于ApiClient单例
  provider: ^6.1.2

  # WebSocket通信库
  web_socket_channel: ^2.4.5

  # Supabase 客户端
  supabase_flutter: ^2.5.8

  # 获取设备信息
  device_info_plus: ^10.1.0

  # 音频播放库
  just_audio: ^0.9.36

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # --- v 新增依赖 v ---
  # 用于截图功能
  screenshot: ^3.0.0 

  # 用于调用原生分享功能
  share_plus: ^9.0.0

  # 用于获取保存截图的临时目录
  path_provider: ^2.1.3
  
  # 用于实现打字机等文本动画
  animated_text_kit: ^4.2.2

  # 文件选择器 - 用于角色卡导入
  file_picker: ^8.0.0+1
  # --- ^ 新增依赖 ^ ---

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

  # --- v 新增图标生成工具 v ---
  flutter_launcher_icons: ^0.13.1
  # --- ^ 新增图标生成工具 ^ ---

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # 在这里添加 fonts 部分
  fonts:
    - family: BaiWuChangKeKeTi
      fonts:
        - asset: assets/fonts/BaiWuChangKeKeTi-Regular.ttf
          # 如果你有不同字重的字体文件，也可以在这里指定
          # weight: 400
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# --- v 新增图标配置 v ---
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # optional
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/images/logo.png"
# --- ^ 新增图标配置 ^ ---
